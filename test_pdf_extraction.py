#!/usr/bin/env python3
"""
Script di test per verificare l'estrazione migliorata dei PDF con supporto tabelle
"""

import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).parent))

from pdf_chatbot_prodotti import FileProcessor
import logging

# Configura logging per il test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pdf_extraction():
    """Testa l'estrazione di testo e tabelle dai PDF"""
    
    # Trova i file PDF nella cartella pdf/SYM
    pdf_dir = Path("pdf/SYM")
    if not pdf_dir.exists():
        logger.error(f"Directory {pdf_dir} non trovata")
        return False
    
    pdf_files = list(pdf_dir.glob("*.pdf"))
    if not pdf_files:
        logger.error(f"Nessun file PDF trovato in {pdf_dir}")
        return False
    
    logger.info(f"Trovati {len(pdf_files)} file PDF da testare")
    
    # Testa l'estrazione per ogni PDF
    for pdf_file in pdf_files[:2]:  # Testa solo i primi 2 per velocità
        logger.info(f"\n{'='*60}")
        logger.info(f"🔍 Test estrazione per: {pdf_file.name}")
        logger.info(f"{'='*60}")
        
        try:
            # Usa il metodo migliorato
            pages_data = FileProcessor.extract_text_from_pdf(str(pdf_file))
            
            if not pages_data:
                logger.warning(f"❌ Nessun dato estratto da {pdf_file.name}")
                continue
            
            logger.info(f"✅ Estratte {len(pages_data)} pagine da {pdf_file.name}")
            
            # Mostra un esempio del contenuto estratto
            for i, page_data in enumerate(pages_data[:2]):  # Solo prime 2 pagine
                page_num = page_data['page_num']
                text = page_data['text']
                
                logger.info(f"\n--- PAGINA {page_num} ---")
                
                # Controlla se ci sono tabelle (cerca pattern Markdown)
                if "###" in text and "|" in text:
                    logger.info("🎯 TABELLE RILEVATE! Contenuto con tabelle:")
                    # Mostra solo le prime righe per brevità
                    lines = text.split('\n')[:15]
                    for line in lines:
                        if line.strip():
                            logger.info(f"  {line}")
                    if len(text.split('\n')) > 15:
                        logger.info("  ... (contenuto troncato)")
                else:
                    logger.info("📄 Contenuto testuale normale:")
                    # Mostra solo le prime 200 caratteri
                    preview = text[:200].replace('\n', ' ')
                    logger.info(f"  {preview}...")
                
        except Exception as e:
            logger.error(f"❌ Errore durante l'estrazione da {pdf_file.name}: {e}")
            return False
    
    logger.info(f"\n{'='*60}")
    logger.info("✅ Test completato con successo!")
    logger.info("🎯 Verifica che le tabelle siano state estratte in formato Markdown")
    logger.info("📋 Cerca pattern come '### Tabella X' e righe con '|'")
    logger.info(f"{'='*60}")
    
    return True

if __name__ == "__main__":
    success = test_pdf_extraction()
    sys.exit(0 if success else 1)
