#!/bin/bash

# Script di avvio per Chat-Jina Production Server v2.0
# Assicurati che questo file sia eseguibile: chmod +x start.sh

echo "🚀 Chat-Jina Production Server v2.0"
echo "===================================="

# Verifica che Python sia installato
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 non trovato. Installa Python 3.8 o superiore."
    exit 1
fi

# Verifica che il file .env esista
if [ ! -f ".env" ]; then
    echo "⚠️  File .env non trovato."
    echo "💡 Copia .env.template in .env e configura le tue API keys:"
    echo "   cp .env.template .env"
    echo "   nano .env"
    exit 1
fi

# Carica le variabili d'ambiente dal file .env
export $(grep -v '^#' .env | xargs)

# Crea le cartelle necessarie
mkdir -p logs
mkdir -p chromadb_data

echo "🔧 Installazione dipendenze..."
pip3 install -r requirements.txt

echo "🚀 Avvio dell'applicazione..."
python3 start_production.py
