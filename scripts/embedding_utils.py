#!/usr/bin/env python3
"""
Utilità condivise per il sistema di embedding automatizzato
"""

import os
import sys
import json
import hashlib
import logging
import psutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import time

# Aggiungi il percorso root al sys.path per importare i moduli del progetto
sys.path.append(str(Path(__file__).parent.parent))

from scripts.embedding_config import EmbeddingConfig

@dataclass
class ProcessingStats:
    """Statistiche di elaborazione"""
    total_files: int = 0
    processed_files: int = 0
    skipped_files: int = 0
    failed_files: int = 0
    total_chunks: int = 0
    total_size_bytes: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    errors: List[str] = None
    # Statistiche tabelle
    total_tables_extracted: int = 0
    files_with_tables: int = 0
    pdf_files_processed: int = 0
    table_extraction_errors: int = 0
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
    
    @property
    def processing_time(self) -> Optional[timedelta]:
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    @property
    def success_rate(self) -> float:
        if self.total_files == 0:
            return 0.0
        return (self.processed_files / self.total_files) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        if self.start_time:
            data['start_time'] = self.start_time.isoformat()
        if self.end_time:
            data['end_time'] = self.end_time.isoformat()
        if self.processing_time:
            data['processing_time_seconds'] = self.processing_time.total_seconds()
        data['success_rate'] = self.success_rate
        return data

@dataclass
class TableExtractionStats:
    """Statistiche specifiche per l'estrazione delle tabelle"""
    file_path: str = ""
    tables_found: int = 0
    pages_with_tables: List[int] = None
    extraction_method: str = ""  # "pdfplumber" o "pymupdf_fallback"
    extraction_time_seconds: float = 0.0
    has_extraction_error: bool = False
    error_message: str = ""

    def __post_init__(self):
        if self.pages_with_tables is None:
            self.pages_with_tables = []

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class FileScanner:
    """Scanner per trovare file da processare"""
    
    def __init__(self, base_dir: Path, supported_extensions: List[str]):
        self.base_dir = base_dir
        self.supported_extensions = [ext.lower() for ext in supported_extensions]
        self.logger = logging.getLogger(__name__)
    
    def scan_files(self, recursive: bool = True) -> List[Path]:
        """Scansiona i file supportati nella directory base"""
        files = []
        
        if not self.base_dir.exists():
            self.logger.error(f"Directory non trovata: {self.base_dir}")
            return files
        
        try:
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in self.base_dir.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                    files.append(file_path)
            
            self.logger.info(f"Trovati {len(files)} file supportati in {self.base_dir}")
            
        except Exception as e:
            self.logger.error(f"Errore durante la scansione di {self.base_dir}: {e}")
        
        return sorted(files)
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """Ottiene informazioni dettagliate su un file"""
        try:
            stat = file_path.stat()
            return {
                'path': str(file_path),
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'extension': file_path.suffix.lower(),
                'product_code': file_path.parent.name if file_path.parent.name != 'pdf' else 'unknown'
            }
        except Exception as e:
            self.logger.error(f"Errore nell'ottenere info per {file_path}: {e}")
            return {'path': str(file_path), 'error': str(e)}

class HashManager:
    """Gestisce gli hash dei file per il controllo delle modifiche"""
    
    def __init__(self, metadata_file: Path):
        self.metadata_file = metadata_file
        self.logger = logging.getLogger(__name__)
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calcola l'hash MD5 di un file"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"Errore nel calcolo dell'hash per {file_path}: {e}")
            return ""
    
    def load_metadata(self) -> Dict[str, Any]:
        """Carica i metadati esistenti"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Errore nel caricamento metadati: {e}")
        return {}
    
    def save_metadata(self, metadata: Dict[str, Any]):
        """Salva i metadati"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Metadati salvati in {self.metadata_file}")
        except Exception as e:
            self.logger.error(f"Errore nel salvataggio metadati: {e}")
    
    def check_file_changed(self, file_path: Path, stored_metadata: Dict[str, Any]) -> bool:
        """Verifica se un file è cambiato rispetto ai metadati memorizzati"""
        path_str = str(file_path)
        current_hash = self.calculate_file_hash(file_path)
        
        if not current_hash:
            return True  # Se non riusciamo a calcolare l'hash, consideriamo il file cambiato
        
        stored_files = stored_metadata.get("files", {})
        return path_str not in stored_files or stored_files[path_str] != current_hash
    
    def get_changed_files(self, files: List[Path]) -> Tuple[List[Path], List[Path]]:
        """Restituisce i file cambiati e quelli non cambiati"""
        metadata = self.load_metadata()
        changed_files = []
        unchanged_files = []
        
        for file_path in files:
            if self.check_file_changed(file_path, metadata):
                changed_files.append(file_path)
            else:
                unchanged_files.append(file_path)
        
        return changed_files, unchanged_files

class PerformanceMonitor:
    """Monitora le performance del sistema durante l'elaborazione"""
    
    def __init__(self):
        self.start_time = None
        self.process = psutil.Process()
        self.initial_memory = None
        self.api_calls = 0
        self.logger = logging.getLogger(__name__)
    
    def start_monitoring(self):
        """Inizia il monitoraggio"""
        self.start_time = time.time()
        self.initial_memory = self.process.memory_info().rss
        self.api_calls = 0
    
    def record_api_call(self):
        """Registra una chiamata API"""
        self.api_calls += 1
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Ottiene le statistiche correnti"""
        if not self.start_time:
            return {}
        
        current_time = time.time()
        current_memory = self.process.memory_info().rss
        
        return {
            'elapsed_time': current_time - self.start_time,
            'memory_usage_mb': current_memory / (1024 * 1024),
            'memory_increase_mb': (current_memory - self.initial_memory) / (1024 * 1024) if self.initial_memory else 0,
            'cpu_percent': self.process.cpu_percent(),
            'api_calls': self.api_calls
        }

class TableContentAnalyzer:
    """Analizza il contenuto estratto per rilevare e analizzare le tabelle"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def analyze_extracted_content(self, pages_data: List[Dict[str, Any]], file_path: str) -> TableExtractionStats:
        """Analizza il contenuto estratto per statistiche sulle tabelle"""
        stats = TableExtractionStats(file_path=file_path)

        try:
            start_time = time.time()

            for page_data in pages_data:
                page_num = page_data.get('page_num', 0)
                text = page_data.get('text', '')

                # Rileva tabelle nel testo (cerca pattern Markdown)
                if self._has_table_markers(text):
                    table_count = self._count_tables_in_text(text)
                    stats.tables_found += table_count
                    if table_count > 0:
                        stats.pages_with_tables.append(page_num)

            # Determina il metodo di estrazione
            if any("### Tabella" in page.get('text', '') for page in pages_data):
                stats.extraction_method = "pdfplumber"
            else:
                stats.extraction_method = "pymupdf_fallback"

            stats.extraction_time_seconds = time.time() - start_time

            self.logger.debug(f"Analisi tabelle per {file_path}: {stats.tables_found} tabelle trovate")

        except Exception as e:
            stats.has_extraction_error = True
            stats.error_message = str(e)
            self.logger.error(f"Errore nell'analisi tabelle per {file_path}: {e}")

        return stats

    def _has_table_markers(self, text: str) -> bool:
        """Verifica se il testo contiene marcatori di tabelle"""
        return ("### Tabella" in text and "|" in text) or ("---" in text and "|" in text)

    def _count_tables_in_text(self, text: str) -> int:
        """Conta il numero di tabelle nel testo"""
        return text.count("### Tabella")

class ReportGenerator:
    """Genera report dettagliati delle operazioni di embedding"""
    
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def generate_report(self, stats: ProcessingStats, performance_stats: Dict[str, Any],
                       table_stats: Optional[List[TableExtractionStats]] = None,
                       output_file: Optional[Path] = None) -> Dict[str, Any]:
        """Genera un report completo"""
        timestamp = datetime.now().isoformat()
        
        report = {
            'timestamp': timestamp,
            'processing_stats': stats.to_dict(),
            'performance_stats': performance_stats,
            'configuration': {
                'chunk_size': self.config.CHUNK_SIZE,
                'chunk_overlap': self.config.CHUNK_OVERLAP,
                'batch_size': self.config.BATCH_SIZE,
                'supported_extensions': self.config.SUPPORTED_EXTENSIONS
            },
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform,
                'cpu_count': os.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3)
            }
        }

        # Aggiungi statistiche tabelle se disponibili
        if table_stats and self.config.TABLE_STATS_IN_REPORT:
            report['table_extraction_stats'] = {
                'total_files_with_tables': len([s for s in table_stats if s.tables_found > 0]),
                'total_tables_extracted': sum(s.tables_found for s in table_stats),
                'extraction_methods': {
                    'pdfplumber': len([s for s in table_stats if s.extraction_method == 'pdfplumber']),
                    'pymupdf_fallback': len([s for s in table_stats if s.extraction_method == 'pymupdf_fallback'])
                },
                'files_with_errors': len([s for s in table_stats if s.has_extraction_error]),
                'detailed_stats': [s.to_dict() for s in table_stats]
            }
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                self.logger.info(f"Report salvato in {output_file}")
            except Exception as e:
                self.logger.error(f"Errore nel salvataggio del report: {e}")
        
        return report
    
    def print_summary(self, stats: ProcessingStats, performance_stats: Dict[str, Any],
                      table_stats: Optional[List[TableExtractionStats]] = None):
        """Stampa un riassunto delle operazioni"""
        print("\n" + "="*60)
        print("📊 REPORT FINALE EMBEDDING")
        print("="*60)
        
        print(f"📁 File totali trovati: {stats.total_files}")
        print(f"✅ File processati: {stats.processed_files}")
        print(f"⏭️  File saltati (non modificati): {stats.skipped_files}")
        print(f"❌ File falliti: {stats.failed_files}")
        print(f"📦 Chunk totali creati: {stats.total_chunks}")
        print(f"💾 Dimensione totale: {stats.total_size_bytes / (1024*1024):.2f} MB")
        
        if stats.processing_time:
            print(f"⏱️  Tempo di elaborazione: {stats.processing_time}")
        
        print(f"📈 Tasso di successo: {stats.success_rate:.1f}%")

        # Statistiche tabelle
        if table_stats:
            files_with_tables = len([s for s in table_stats if s.tables_found > 0])
            total_tables = sum(s.tables_found for s in table_stats)
            pdfplumber_files = len([s for s in table_stats if s.extraction_method == 'pdfplumber'])

            print(f"\n📋 STATISTICHE TABELLE:")
            print(f"📄 File PDF processati: {stats.pdf_files_processed}")
            print(f"📊 File con tabelle: {files_with_tables}")
            print(f"🔢 Tabelle totali estratte: {total_tables}")
            print(f"🔧 Estrazione con pdfplumber: {pdfplumber_files}")
            print(f"⚠️  Errori estrazione tabelle: {stats.table_extraction_errors}")

        if performance_stats:
            print(f"\n🔧 Memoria utilizzata: {performance_stats.get('memory_usage_mb', 0):.1f} MB")
            print(f"🌐 Chiamate API: {performance_stats.get('api_calls', 0)}")

        if stats.errors:
            print(f"\n⚠️  Errori riscontrati ({len(stats.errors)}):")
            for error in stats.errors[:5]:  # Mostra solo i primi 5 errori
                print(f"   • {error}")
            if len(stats.errors) > 5:
                print(f"   ... e altri {len(stats.errors) - 5} errori")
        
        print("="*60)

def setup_logging(script_name: str, log_level: str = "INFO") -> logging.Logger:
    """Configura il sistema di logging"""
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))

    # Rimuovi handler esistenti
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Handler per console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # Handler per file
    log_file = EmbeddingConfig.get_log_file_path(script_name)
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, log_level.upper()))
    file_formatter = logging.Formatter(EmbeddingConfig.LOG_FORMAT)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    return logger

def validate_environment() -> List[str]:
    """Valida l'ambiente di esecuzione"""
    errors = []

    # Verifica configurazione
    config_errors = EmbeddingConfig.validate_config()
    errors.extend(config_errors)

    # Verifica dipendenze Python
    required_modules = ['chromadb', 'requests', 'fitz', 'tqdm', 'psutil']
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            errors.append(f"Modulo Python mancante: {module}")

    return errors
