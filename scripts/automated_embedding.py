#!/usr/bin/env python3
"""
Script automatizzato per l'embedding di tutti i documenti nella knowledge base

Funzionalità:
- Scansione ricorsiva di directory in pdf/ per documenti (PDF, DOC, TXT, MD)
- Progress bar e logging dettagliato
- Modalità incrementale: processare solo documenti nuovi/modificati
- Report finale con statistiche complete
- Supporto per esecuzione in background

Utilizzo:
    python scripts/automated_embedding.py [opzioni]

Opzioni:
    --force-reprocess    Riprocessa tutti i file anche se non modificati
    --dry-run           Simula l'esecuzione senza modificare il database
    --product-code      Processa solo un codice prodotto specifico
    --background        Esegue in modalità background (senza progress bar)
    --verbose           Output dettagliato
    --config-file       File di configurazione personalizzato
"""

import sys
import argparse
import signal
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import json

# Aggiungi il percorso root al sys.path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from tqdm import tqdm
except ImportError:
    print("⚠️  Modulo 'tqdm' non trovato. Installalo con: pip install tqdm")
    sys.exit(1)

from scripts.embedding_config import EmbeddingConfig
from scripts.embedding_utils import (
    ProcessingStats, FileScanner, HashManager, PerformanceMonitor,
    ReportGenerator, TableContentAnalyzer, TableExtractionStats,
    setup_logging, validate_environment
)

# Importa le classi dal sistema esistente
from pdf_chatbot_prodotti import ProductChatbot, FileProcessor

class AutomatedEmbedding:
    """Classe principale per l'embedding automatizzato"""
    
    def __init__(self, config: EmbeddingConfig, args: argparse.Namespace):
        self.config = config
        self.args = args
        self.logger = setup_logging("automated_embedding", 
                                   "DEBUG" if args.verbose else config.LOG_LEVEL)
        
        # Inizializza componenti
        self.scanner = FileScanner(config.PDF_BASE_DIR, config.SUPPORTED_EXTENSIONS)
        self.hash_manager = HashManager(config.METADATA_FILE)
        self.performance_monitor = PerformanceMonitor()
        self.report_generator = ReportGenerator(config)
        self.table_analyzer = TableContentAnalyzer()

        # Statistiche
        self.stats = ProcessingStats()
        self.table_stats = []  # Lista per statistiche tabelle
        
        # Chatbot per l'embedding
        self.chatbot = None
        
        # Flag per interruzione graceful
        self.interrupted = False
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Gestisce l'interruzione graceful"""
        self.logger.info("Ricevuto segnale di interruzione. Terminazione in corso...")
        self.interrupted = True
    
    def _initialize_chatbot(self) -> bool:
        """Inizializza il chatbot per l'embedding"""
        try:
            api_keys = self.config.get_api_keys()
            self.chatbot = ProductChatbot(
                jina_api_key=api_keys['jina_api_key'],
                gemini_api_key=api_keys['gemini_api_key'],
                force_reprocess=self.args.force_reprocess
            )
            self.logger.info("✅ Chatbot inizializzato con successo")
            return True
        except Exception as e:
            self.logger.error(f"❌ Errore nell'inizializzazione del chatbot: {e}")
            return False
    
    def _scan_files(self) -> List[Path]:
        """Scansiona i file da processare"""
        self.logger.info("🔍 Scansione file in corso...")
        
        if self.args.product_code:
            # Scansiona solo un prodotto specifico
            product_dir = self.config.PDF_BASE_DIR / self.args.product_code
            scanner = FileScanner(product_dir, self.config.SUPPORTED_EXTENSIONS)
            files = scanner.scan_files(recursive=True)
            self.logger.info(f"Trovati {len(files)} file per il prodotto {self.args.product_code}")
        else:
            # Scansiona tutti i prodotti
            files = self.scanner.scan_files(recursive=True)
            self.logger.info(f"Trovati {len(files)} file totali")
        
        return files
    
    def _filter_files_to_process(self, files: List[Path]) -> List[Path]:
        """Filtra i file che necessitano di elaborazione"""
        if self.args.force_reprocess:
            self.logger.info("🔄 Modalità force-reprocess: tutti i file saranno processati")
            return files
        
        self.logger.info("🔍 Controllo file modificati...")
        changed_files, unchanged_files = self.hash_manager.get_changed_files(files)
        
        self.stats.skipped_files = len(unchanged_files)
        self.logger.info(f"📝 File da processare: {len(changed_files)}")
        self.logger.info(f"⏭️  File saltati (non modificati): {len(unchanged_files)}")
        
        return changed_files
    
    def _process_single_file(self, file_path: Path) -> bool:
        """Processa un singolo file"""
        try:
            if self.interrupted:
                return False
            
            self.logger.debug(f"Elaborazione file: {file_path}")
            
            if self.args.dry_run:
                self.logger.info(f"[DRY RUN] Simulo elaborazione di {file_path}")
                time.sleep(0.1)  # Simula tempo di elaborazione
                return True
            
            # Determina il codice prodotto dal percorso
            # Risale la gerarchia fino a trovare la directory del prodotto
            current_path = file_path.parent
            product_code = 'unknown'

            # Cerca la directory del prodotto (quella sotto pdf/)
            while current_path.name != 'pdf' and current_path.parent != current_path:
                if current_path.parent.name == 'pdf':
                    product_code = current_path.name
                    break
                current_path = current_path.parent

            # Se è specificato un product_code negli argomenti, usalo
            if self.args.product_code:
                product_code = self.args.product_code
            
            # Analizza tabelle per file PDF prima dell'elaborazione
            if file_path.suffix.lower() == '.pdf' and self.config.TABLE_EXTRACTION_ENABLED:
                try:
                    self.stats.pdf_files_processed += 1

                    # Estrai contenuto per analisi tabelle
                    pages_data = FileProcessor.extract_text_from_pdf(str(file_path))

                    # Analizza tabelle
                    table_stats = self.table_analyzer.analyze_extracted_content(pages_data, str(file_path))
                    self.table_stats.append(table_stats)

                    # Aggiorna statistiche globali
                    if table_stats.tables_found > 0:
                        self.stats.files_with_tables += 1
                        self.stats.total_tables_extracted += table_stats.tables_found

                    if table_stats.has_extraction_error:
                        self.stats.table_extraction_errors += 1

                    self.logger.debug(f"Tabelle trovate in {file_path.name}: {table_stats.tables_found}")

                except Exception as e:
                    self.logger.warning(f"Errore nell'analisi tabelle per {file_path}: {e}")
                    self.stats.table_extraction_errors += 1

            # Usa il metodo esistente del chatbot per l'embedding
            files_to_process = [file_path]
            self.chatbot._process_and_load_files(files_to_process, product_code)

            # Aggiorna statistiche
            file_size = file_path.stat().st_size
            self.stats.total_size_bytes += file_size

            return True
            
        except Exception as e:
            error_msg = f"Errore nell'elaborazione di {file_path}: {e}"
            self.logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False
    
    def _process_files(self, files: List[Path]):
        """Processa tutti i file con progress bar"""
        self.stats.total_files = len(files)
        self.stats.start_time = datetime.now()
        
        if not files:
            self.logger.info("✅ Nessun file da processare")
            return
        
        self.logger.info(f"🚀 Inizio elaborazione di {len(files)} file...")
        
        # Configura progress bar
        if self.args.background:
            # Modalità background: nessuna progress bar
            progress_bar = None
        else:
            progress_bar = tqdm(
                files, 
                desc="Elaborazione file",
                unit="file",
                ncols=100,
                bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]"
            )
        
        # Processa i file
        for file_path in (progress_bar if progress_bar else files):
            if self.interrupted:
                self.logger.warning("⚠️  Elaborazione interrotta dall'utente")
                break
            
            success = self._process_single_file(file_path)
            
            if success:
                self.stats.processed_files += 1
            else:
                self.stats.failed_files += 1
            
            # Aggiorna progress bar
            if progress_bar:
                progress_bar.set_postfix({
                    'Successi': self.stats.processed_files,
                    'Errori': self.stats.failed_files
                })
        
        if progress_bar:
            progress_bar.close()
        
        self.stats.end_time = datetime.now()
    
    def _update_metadata(self, files: List[Path]):
        """Aggiorna i metadati con gli hash dei file processati"""
        if self.args.dry_run:
            self.logger.info("[DRY RUN] Simulo aggiornamento metadati")
            return
        
        self.logger.info("💾 Aggiornamento metadati...")
        metadata = self.hash_manager.load_metadata()
        
        if "files" not in metadata:
            metadata["files"] = {}
        
        for file_path in files:
            if not self.interrupted:
                file_hash = self.hash_manager.calculate_file_hash(file_path)
                if file_hash:
                    metadata["files"][str(file_path)] = file_hash
        
        metadata["last_processed"] = datetime.now().isoformat()
        self.hash_manager.save_metadata(metadata)
    
    def run(self) -> bool:
        """Esegue l'embedding automatizzato"""
        try:
            self.logger.info("🚀 Avvio embedding automatizzato")
            self.performance_monitor.start_monitoring()
            
            # Valida ambiente
            env_errors = validate_environment()
            if env_errors:
                for error in env_errors:
                    self.logger.error(f"❌ {error}")
                return False
            
            # Inizializza chatbot
            if not self.args.dry_run and not self._initialize_chatbot():
                return False
            
            # Scansiona file
            all_files = self._scan_files()
            if not all_files:
                self.logger.info("✅ Nessun file trovato da processare")
                return True
            
            # Filtra file da processare
            files_to_process = self._filter_files_to_process(all_files)
            
            # Processa file
            self._process_files(files_to_process)
            
            # Aggiorna metadati
            if files_to_process:
                self._update_metadata(files_to_process)
            
            # Genera report
            performance_stats = self.performance_monitor.get_current_stats()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = self.config.get_report_file_path(timestamp)
            
            self.report_generator.generate_report(
                self.stats, performance_stats, self.table_stats, report_file
            )
            self.report_generator.print_summary(self.stats, performance_stats, self.table_stats)
            
            success = self.stats.failed_files == 0 and not self.interrupted
            if success:
                self.logger.info("✅ Embedding completato con successo!")
            else:
                self.logger.warning("⚠️  Embedding completato con alcuni errori")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Errore fatale: {e}")
            return False

def parse_arguments() -> argparse.Namespace:
    """Parsing degli argomenti da linea di comando"""
    parser = argparse.ArgumentParser(
        description="Script automatizzato per l'embedding dei documenti",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--force-reprocess", 
        action="store_true",
        help="Riprocessa tutti i file anche se non modificati"
    )
    
    parser.add_argument(
        "--dry-run", 
        action="store_true",
        help="Simula l'esecuzione senza modificare il database"
    )
    
    parser.add_argument(
        "--product-code", 
        type=str,
        help="Processa solo un codice prodotto specifico"
    )
    
    parser.add_argument(
        "--background", 
        action="store_true",
        help="Esegue in modalità background (senza progress bar)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Output dettagliato"
    )
    
    parser.add_argument(
        "--config-file", 
        type=Path,
        help="File di configurazione personalizzato"
    )
    
    return parser.parse_args()

def main():
    """Funzione principale"""
    args = parse_arguments()
    
    # Carica configurazione
    config = EmbeddingConfig()
    
    # Crea ed esegue l'embedding automatizzato
    embedder = AutomatedEmbedding(config, args)
    success = embedder.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
