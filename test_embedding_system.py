#!/usr/bin/env python3
"""
Test completo del sistema di embedding con supporto tabelle
"""

import sys
import os
import subprocess
from pathlib import Path
from dotenv import load_dotenv
import logging
import json

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).parent))

# Carica le variabili d'ambiente
load_dotenv()

# Configura logging per il test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_embedding_system():
    """Testa l'intero sistema di embedding con le nuove funzionalità"""
    
    logger.info("🚀 Avvio test completo sistema embedding...")
    
    # Verifica prerequisiti
    if not _check_prerequisites():
        return False
    
    # Test 1: Verifica configurazione
    logger.info("\n" + "="*60)
    logger.info("📋 TEST 1: Verifica Configurazione")
    logger.info("="*60)
    
    if not _test_configuration():
        logger.error("❌ Test configurazione fallito")
        return False
    
    # Test 2: Test estrazione tabelle standalone
    logger.info("\n" + "="*60)
    logger.info("📊 TEST 2: Estrazione Tabelle Standalone")
    logger.info("="*60)
    
    if not _test_table_extraction():
        logger.error("❌ Test estrazione tabelle fallito")
        return False
    
    # Test 3: Test sistema embedding completo
    logger.info("\n" + "="*60)
    logger.info("🔧 TEST 3: Sistema Embedding Completo")
    logger.info("="*60)
    
    if not _test_full_embedding_system():
        logger.error("❌ Test sistema embedding fallito")
        return False
    
    # Test 4: Verifica report e statistiche
    logger.info("\n" + "="*60)
    logger.info("📊 TEST 4: Report e Statistiche")
    logger.info("="*60)
    
    if not _test_reports_and_stats():
        logger.error("❌ Test report e statistiche fallito")
        return False
    
    logger.info("\n" + "="*60)
    logger.info("✅ TUTTI I TEST COMPLETATI CON SUCCESSO!")
    logger.info("🎯 Il sistema di embedding è pronto con supporto tabelle")
    logger.info("="*60)
    
    return True

def _check_prerequisites():
    """Verifica i prerequisiti per i test"""
    logger.info("🔍 Verifica prerequisiti...")
    
    # Verifica API keys
    jina_key = os.getenv("JINA_API_KEY")
    gemini_key = os.getenv("GEMINI_API_KEY")
    
    if not jina_key:
        logger.error("❌ JINA_API_KEY non trovata")
        return False
    
    if not gemini_key:
        logger.error("❌ GEMINI_API_KEY non trovata")
        return False
    
    # Verifica directory PDF
    pdf_dir = Path("pdf")
    if not pdf_dir.exists():
        logger.error("❌ Directory pdf/ non trovata")
        return False
    
    # Verifica file PDF di test
    test_files = list(pdf_dir.glob("**/*.pdf"))
    if not test_files:
        logger.error("❌ Nessun file PDF trovato per i test")
        return False
    
    logger.info(f"✅ Trovati {len(test_files)} file PDF per i test")
    
    # Verifica ChromaDB
    try:
        import requests
        response = requests.get("http://localhost:8000/api/v2/auth/identity", timeout=5)
        if response.status_code != 200:
            logger.error("❌ ChromaDB non risponde correttamente")
            return False
        logger.info("✅ ChromaDB attivo e raggiungibile")
    except Exception as e:
        logger.error(f"❌ Errore connessione ChromaDB: {e}")
        return False
    
    # Verifica pdfplumber installato
    try:
        import pdfplumber
        logger.info("✅ pdfplumber installato correttamente")
    except ImportError:
        logger.error("❌ pdfplumber non installato")
        return False
    
    return True

def _test_configuration():
    """Testa la configurazione del sistema"""
    try:
        from scripts.embedding_config import EmbeddingConfig
        
        # Verifica configurazioni tabelle
        assert hasattr(EmbeddingConfig, 'TABLE_EXTRACTION_ENABLED'), "TABLE_EXTRACTION_ENABLED mancante"
        assert hasattr(EmbeddingConfig, 'TABLE_MARKDOWN_FORMAT'), "TABLE_MARKDOWN_FORMAT mancante"
        assert hasattr(EmbeddingConfig, 'TRACK_TABLE_STATISTICS'), "TRACK_TABLE_STATISTICS mancante"
        
        logger.info("✅ Configurazioni tabelle presenti")
        
        # Verifica validazione configurazione
        errors = EmbeddingConfig.validate_config()
        if errors:
            logger.warning(f"⚠️ Errori configurazione: {errors}")
        else:
            logger.info("✅ Configurazione valida")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore test configurazione: {e}")
        return False

def _test_table_extraction():
    """Testa l'estrazione delle tabelle"""
    try:
        from pdf_chatbot_prodotti import FileProcessor
        
        # Trova un file PDF di test
        pdf_files = list(Path("pdf").glob("**/*.pdf"))
        if not pdf_files:
            logger.error("❌ Nessun file PDF per test")
            return False
        
        test_file = pdf_files[0]
        logger.info(f"🔍 Test estrazione su: {test_file.name}")
        
        # Estrai contenuto
        pages_data = FileProcessor.extract_text_from_pdf(str(test_file))
        
        if not pages_data:
            logger.error("❌ Nessun contenuto estratto")
            return False
        
        logger.info(f"✅ Estratte {len(pages_data)} pagine")
        
        # Verifica presenza tabelle
        tables_found = 0
        for page_data in pages_data:
            text = page_data.get('text', '')
            if "### Tabella" in text and "|" in text:
                tables_found += text.count("### Tabella")
        
        logger.info(f"📊 Tabelle trovate: {tables_found}")
        
        if tables_found > 0:
            logger.info("✅ Estrazione tabelle funzionante")
        else:
            logger.info("ℹ️ Nessuna tabella nel file di test (normale)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore test estrazione tabelle: {e}")
        return False

def _test_full_embedding_system():
    """Testa il sistema di embedding completo"""
    try:
        # Esegui lo script di embedding automatizzato in modalità dry-run
        script_path = Path("scripts/automated_embedding.py")
        
        cmd = [
            sys.executable, str(script_path),
            "--dry-run",
            "--verbose",
            "--product-code", "SYM"  # Usa SYM come test
        ]
        
        logger.info("🔧 Esecuzione embedding automatizzato (dry-run)...")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60  # Timeout di 1 minuto per dry-run
        )
        
        if result.returncode != 0:
            logger.error(f"❌ Script embedding fallito: {result.stderr}")
            return False
        
        logger.info("✅ Script embedding eseguito con successo")
        
        # Verifica output per statistiche tabelle
        output = result.stdout
        if "STATISTICHE TABELLE" in output:
            logger.info("✅ Statistiche tabelle presenti nell'output")
        else:
            logger.info("ℹ️ Statistiche tabelle non presenti (normale per dry-run)")
        
        return True
        
    except subprocess.TimeoutExpired:
        logger.error("❌ Timeout esecuzione script embedding")
        return False
    except Exception as e:
        logger.error(f"❌ Errore test sistema embedding: {e}")
        return False

def _test_reports_and_stats():
    """Testa la generazione di report e statistiche"""
    try:
        from scripts.embedding_utils import (
            ProcessingStats, TableExtractionStats, 
            TableContentAnalyzer, ReportGenerator
        )
        from scripts.embedding_config import EmbeddingConfig
        
        # Test statistiche tabelle
        stats = TableExtractionStats(
            file_path="test.pdf",
            tables_found=3,
            pages_with_tables=[1, 2, 5],
            extraction_method="pdfplumber"
        )
        
        stats_dict = stats.to_dict()
        assert 'tables_found' in stats_dict
        assert stats_dict['tables_found'] == 3
        
        logger.info("✅ Statistiche tabelle funzionanti")
        
        # Test report generator
        config = EmbeddingConfig()
        report_gen = ReportGenerator(config)
        
        processing_stats = ProcessingStats()
        processing_stats.total_files = 5
        processing_stats.processed_files = 4
        processing_stats.files_with_tables = 2
        processing_stats.total_tables_extracted = 8
        
        performance_stats = {'memory_usage_mb': 100, 'api_calls': 10}
        table_stats = [stats]
        
        report = report_gen.generate_report(
            processing_stats, 
            performance_stats, 
            table_stats
        )
        
        # Verifica presenza sezioni tabelle nel report
        assert 'table_extraction_stats' in report
        assert 'total_tables_extracted' in report['table_extraction_stats']
        
        logger.info("✅ Generazione report con statistiche tabelle funzionante")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore test report e statistiche: {e}")
        return False

if __name__ == "__main__":
    success = test_embedding_system()
    sys.exit(0 if success else 1)
