#!/bin/bash
echo "🚀 Avvio ChromaDB Softway in background..."

# Crea directory se non esiste
mkdir -p chromadb_data_softway

# Avvia ChromaDB in background
python3 -m chromadb.cli.cli run \
  --host 0.0.0.0 \
  --port 9002 \
  --path ./chromadb_data_softway \
  > chromadb_softway.log 2>&1 &

CHROMADB_PID=$!
echo "📍 ChromaDB avviato con PID: $CHROMADB_PID"
echo "📁 Directory dati: $(pwd)/chromadb_data_softway"
echo "🌐 Endpoint: http://localhost:9002"
echo "📋 Log file: $(pwd)/chromadb_softway.log"

# Aspetta e verifica
sleep 3

if kill -0 $CHROMADB_PID 2>/dev/null; then
    echo "✅ ChromaDB è attivo e funzionante!"
    echo "🔍 Per verificare: lsof -i :9002"
    echo "📋 Per vedere i log: tail -f chromadb_softway.log"
    echo "⏹️  Per fermarlo: kill $CHROMADB_PID"
else
    echo "❌ ChromaDB non è riuscito ad avviarsi"
    echo "📋 Controlla i log:"
    cat chromadb_softway.log
fi