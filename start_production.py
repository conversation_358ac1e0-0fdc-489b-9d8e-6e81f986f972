#!/usr/bin/env python3
"""
Script di avvio ottimizzato per la produzione del chatbot Chat-Jina.
Questo script configura l'ambiente di produzione e avvia l'applicazione Flask
con le ottimizzazioni necessarie per un ambiente di produzione.
"""

import os
import sys
import logging
import signal
from pathlib import Path
from dotenv import load_dotenv

def setup_production_environment():
    """Configura l'ambiente di produzione."""
    # Imposta le variabili d'ambiente per la produzione
    os.environ.setdefault('FLASK_ENV', 'production')
    os.environ.setdefault('FLASK_DEBUG', 'False')
    os.environ.setdefault('VERBOSITY_LEVEL', '2')
    
    # Crea le cartelle necessarie
    Path('logs').mkdir(exist_ok=True)
    Path('chromadb_data').mkdir(exist_ok=True)
    
    # Configura il logging per la produzione
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/app_production.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_environment():
    """Verifica che tutte le variabili d'ambiente necessarie siano impostate."""
    required_vars = ['JINA_API_KEY', 'GEMINI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logging.error(f"❌ Variabili d'ambiente mancanti: {', '.join(missing_vars)}")
        logging.error("💡 Assicurati di aver creato il file .env con le configurazioni necessarie.")
        logging.error("💡 Puoi usare .env.template come riferimento.")
        sys.exit(1)
    
    logging.info("✅ Tutte le variabili d'ambiente necessarie sono impostate.")

def optimize_for_production():
    """Applica ottimizzazioni specifiche per la produzione."""
    from performance_config import PerformanceProfiles
    
    # Applica il profilo di performance bilanciato per la produzione
    profile = os.getenv('PERFORMANCE_PROFILE', 'balanced')
    
    if profile == 'fast':
        PerformanceProfiles.apply_fast_profile()
        logging.info("🚀 Profilo performance: VELOCE")
    elif profile == 'quality':
        PerformanceProfiles.apply_quality_profile()
        logging.info("🎯 Profilo performance: QUALITÀ")
    else:
        PerformanceProfiles.apply_balanced_profile()
        logging.info("⚖️ Profilo performance: BILANCIATO")

def main():
    """Funzione principale per l'avvio dell'applicazione."""
    print("🚀 Chat-Jina Production Server v2.0")
    print("=" * 50)
    
    # Setup dell'ambiente di produzione
    load_dotenv()
    setup_production_environment()
    logging.info("🔧 Configurazione ambiente di produzione completata.")
    
    # Verifica delle variabili d'ambiente
    check_environment()
    
    # Ottimizzazioni per la produzione
    optimize_for_production()
    
    # Importa e avvia l'applicazione Flask
    try:
        from app import app
        logging.info("📦 Applicazione Flask caricata con successo.")
        
        # Configurazioni specifiche per la produzione
        host = os.getenv('HOST', '0.0.0.0')
        port = int(os.getenv('PORT', '5001'))
        
        logging.info(f"🌐 Avvio server su {host}:{port}")
        logging.info("✅ Applicazione pronta per ricevere richieste.")
        
        # Avvia l'applicazione con Gunicorn
        from gunicorn.app.base import BaseApplication

        class StandaloneApplication(BaseApplication):
            def __init__(self, app, options=None):
                self.options = options or {}
                self.application = app
                super().__init__()

            def load_config(self):
                config = {key: value for key, value in self.options.items()
                          if key in self.cfg.settings and value is not None}
                for key, value in config.items():
                    self.cfg.set(key.lower(), value)

            def load(self):
                return self.application

        options = {
            'bind': '%s:%s' % (host, port),
            'workers': 1,
            'threads': 4,
            'worker_class': 'gthread',
            'loglevel': 'info',
            'accesslog': 'logs/gunicorn_access.log',
            'errorlog': 'logs/gunicorn_error.log',
            'proc_name': 'gunicorn_infocar_chatbot',
            'timeout': 120,
            'preload_app': True
        }

        StandaloneApplication(app, options).run()
        
    except Exception as e:
        logging.error(f"❌ Errore durante l'avvio dell'applicazione: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
