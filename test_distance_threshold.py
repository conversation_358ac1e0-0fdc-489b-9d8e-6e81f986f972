#!/usr/bin/env python3
"""
Script di test rapido per verificare il controllo delle distances
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).parent))

# Carica le variabili d'ambiente
load_dotenv()

from pdf_chatbot_prodotti import ProductChatbot
import logging

# Configura logging per il test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_distance_threshold():
    """Testa rapidamente il controllo delle distances"""
    
    # Verifica le API keys
    jina_api_key = os.getenv("JINA_API_KEY")
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    if not jina_api_key or not gemini_api_key:
        logger.error("❌ API keys non configurate. Controlla il file .env")
        return False
    
    logger.info("🚀 Inizializzazione chatbot per test distance...")
    
    try:
        # Inizializza il chatbot
        chatbot = ProductChatbot(
            jina_api_key=jina_api_key,
            gemini_api_key=gemini_api_key,
            verbosity_level=3,
            force_reprocess=False
        )
        
        # Prepara i documenti per SYM
        product_code = "SYM"
        documents_ready = chatbot.prepare_product_documents(product_code)
        
        if not documents_ready:
            logger.error(f"❌ Documenti non pronti per {product_code}")
            return False
        
        logger.info(f"✅ Documenti pronti per {product_code}")
        
        # Test con domande molto specifiche vs generiche
        test_questions = [
            {
                "question": "coppia massima motore",
                "type": "molto specifica",
                "expected_fallback": False
            },
            {
                "question": "hello world programming",
                "type": "completamente irrilevante",
                "expected_fallback": True
            },
            {
                "question": "cilindrata",
                "type": "specifica",
                "expected_fallback": False
            }
        ]
        
        logger.info(f"\n{'='*60}")
        logger.info("🧪 TEST CONTROLLO DISTANCES")
        logger.info(f"{'='*60}")
        
        for i, test_case in enumerate(test_questions, 1):
            question = test_case["question"]
            question_type = test_case["type"]
            expected_fallback = test_case["expected_fallback"]
            
            logger.info(f"\n--- TEST {i}/3 ---")
            logger.info(f"❓ Domanda ({question_type}): {question}")
            
            try:
                # Esegui la query con timeout ridotto per evitare attese lunghe
                response, _, guardrail_data = chatbot.search_and_answer(
                    query=question,
                    product_code=product_code,
                    chat_history=[],
                    n_results=3  # Riduci il numero di risultati per velocità
                )
                
                # Verifica se è stata attivata la risposta di fallback
                is_fallback = "Non ho trovato informazioni pertinenti nei documenti" in response
                is_timeout = "La ricerca sta richiedendo troppo tempo" in response
                
                logger.info(f"🤖 Risposta: {response[:80]}...")
                logger.info(f"🎯 Fallback attivato: {'SÌ' if is_fallback else 'NO'}")
                logger.info(f"⏰ Timeout: {'SÌ' if is_timeout else 'NO'}")
                logger.info(f"✅ Comportamento atteso: {'SÌ' if expected_fallback else 'NO'}")
                
                # Il test passa se:
                # 1. È il comportamento atteso (fallback o risposta normale)
                # 2. O se va in timeout (che è comunque un fallback accettabile)
                test_passed = (is_fallback == expected_fallback) or (is_timeout and expected_fallback)
                logger.info(f"📊 Test: {'✅ PASSATO' if test_passed else '❌ FALLITO'}")
                
            except Exception as e:
                logger.error(f"❌ Errore durante il test: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante l'inizializzazione: {e}")
        return False

if __name__ == "__main__":
    success = test_distance_threshold()
    sys.exit(0 if success else 1)
