#!/usr/bin/env python3
"""
Script di test specifico per verificare l'estrazione delle tabelle
"""

import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).parent))

from pdf_chatbot_prodotti import FileProcessor
import logging

# Configura logging per il test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_table_extraction():
    """Testa specificamente l'estrazione delle tabelle"""
    
    # Testa con il manuale che sappiamo contenere tabelle
    pdf_file = Path("pdf/SYM/Symphony ST125 200 E5 Manuale uso e manutenzione.pdf")
    
    if not pdf_file.exists():
        logger.error(f"File {pdf_file} non trovato")
        return False
    
    logger.info(f"🔍 Test estrazione tabelle da: {pdf_file.name}")
    
    try:
        # Estrai tutte le pagine
        pages_data = FileProcessor.extract_text_from_pdf(str(pdf_file))
        
        if not pages_data:
            logger.error("❌ Nessun dato estratto")
            return False
        
        # Cerca pagine con tabelle
        table_pages = []
        for page_data in pages_data:
            text = page_data['text']
            if "### Tabella" in text and "|" in text:
                table_pages.append(page_data)
        
        logger.info(f"✅ Trovate {len(table_pages)} pagine con tabelle")
        
        # Mostra il contenuto delle prime tabelle trovate
        for i, page_data in enumerate(table_pages[:3]):  # Prime 3 pagine con tabelle
            page_num = page_data['page_num']
            text = page_data['text']
            
            logger.info(f"\n{'='*60}")
            logger.info(f"📋 PAGINA {page_num} - CONTENUTO TABELLE:")
            logger.info(f"{'='*60}")
            
            # Estrai solo la parte delle tabelle
            lines = text.split('\n')
            in_table = False
            table_content = []
            
            for line in lines:
                if "### Tabella" in line:
                    in_table = True
                    table_content.append(line)
                elif in_table and line.strip():
                    if "|" in line or "---" in line:
                        table_content.append(line)
                    elif line.strip() and not line.startswith("###"):
                        # Fine della tabella se non è una riga di tabella
                        if not any(char in line for char in ["|", "---"]):
                            in_table = False
                elif in_table and not line.strip():
                    table_content.append(line)
            
            # Mostra il contenuto della tabella
            for line in table_content[:20]:  # Prime 20 righe
                logger.info(f"  {line}")
            
            if len(table_content) > 20:
                logger.info("  ... (contenuto troncato)")
        
        # Test specifico per cercare dati tecnici comuni
        logger.info(f"\n{'='*60}")
        logger.info("🔍 RICERCA DATI TECNICI SPECIFICI:")
        logger.info(f"{'='*60}")
        
        all_text = " ".join([page['text'] for page in pages_data])
        
        # Cerca termini tecnici comuni
        search_terms = [
            "cilindrata", "coppia", "serraggio", "kg-m", "cc", "rpm", 
            "velocità", "peso", "dimensioni", "capacità"
        ]
        
        for term in search_terms:
            if term.lower() in all_text.lower():
                logger.info(f"✅ Trovato termine: '{term}'")
                # Trova il contesto
                lines = all_text.split('\n')
                for line in lines:
                    if term.lower() in line.lower() and "|" in line:
                        logger.info(f"   Contesto tabella: {line.strip()}")
                        break
            else:
                logger.info(f"❌ Non trovato: '{term}'")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante il test: {e}")
        return False

if __name__ == "__main__":
    success = test_table_extraction()
    sys.exit(0 if success else 1)
