"""
Database manager for conversation logging in MySQL.
Handles connections, connection pooling, and CRUD operations.
"""

import os
import json
import logging
import mysql.connector
from mysql.connector import pooling, Error
from datetime import datetime
from typing import Optional, Dict, List, Any
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages MySQL database connections and operations for conversation logging."""
    
    def __init__(self):
        """Initialize database manager with connection pool."""
        self.pool = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize MySQL connection pool."""
        try:
            pool_config = {
                'pool_name': 'chat_jina_pool',
                'pool_size': int(os.getenv('DB_POOL_SIZE', 10)),
                'pool_reset_session': True,
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': int(os.getenv('DB_PORT', 3306)),
                'database': os.getenv('DB_NAME', 'chat_jina'),
                'user': os.getenv('DB_USER', 'prova'),
                'password': os.getenv('DB_PASSWORD', 'prova'),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci',
                'autocommit': True,
                'raise_on_warnings': True
            }
            
            self.pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("Database connection pool initialized successfully")
            
        except Error as e:
            logger.error(f"Error creating connection pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections."""
        connection = None
        try:
            connection = self.pool.get_connection()
            yield connection
        except Error as e:
            logger.error(f"Database connection error: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def test_connection(self) -> bool:
        """Test database connectivity."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                return result[0] == 1
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def log_conversation(self, conversation_data: Dict[str, Any]) -> Optional[int]:
        """
        Log a conversation to the database.
        
        Args:
            conversation_data: Dictionary containing conversation details
            
        Returns:
            ID of the inserted record or None if failed
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Prepare the SQL query
                query = """
                INSERT INTO conversations 
                (ip_address, product, question, response, guardrail_log, 
                 session_id, user_agent, response_time_ms, confidence_score)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                # Prepare the data tuple
                guardrail_log = conversation_data.get('guardrail_log')
                guardrail_json = None

                if guardrail_log is not None:
                    if isinstance(guardrail_log, list):
                        # If it's already a list (new format), use it directly
                        guardrail_json = json.dumps(guardrail_log)
                    elif isinstance(guardrail_log, dict):
                        # If it's a dict (old format), convert it
                        guardrail_json = json.dumps(guardrail_log)
                    else:
                        # If it's something else, try to serialize it
                        guardrail_json = json.dumps(guardrail_log)

                data = (
                    conversation_data.get('ip_address'),
                    conversation_data.get('product'),
                    conversation_data.get('question'),
                    conversation_data.get('response'),
                    guardrail_json,
                    conversation_data.get('session_id'),
                    conversation_data.get('user_agent'),
                    conversation_data.get('response_time_ms'),
                    conversation_data.get('confidence_score')
                )
                
                cursor.execute(query, data)
                record_id = cursor.lastrowid
                cursor.close()
                
                logger.info(f"Conversation logged successfully with ID: {record_id}")
                return record_id
                
        except Error as e:
            logger.error(f"Error logging conversation: {e}")
            return None
    
    def get_conversations(self, filters: Dict[str, Any] = None, limit: int = 100, offset: int = 0) -> List[Dict]:
        """
        Retrieve conversations with optional filtering.
        
        Args:
            filters: Dictionary of filter criteria
            limit: Maximum number of records to return
            offset: Number of records to skip
            
        Returns:
            List of conversation dictionaries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                
                # Build the query with filters
                query = "SELECT * FROM conversations"
                params = []
                
                if filters:
                    conditions = []
                    for key, value in filters.items():
                        if key in ['ip_address', 'product', 'session_id']:
                            conditions.append(f"{key} = %s")
                            params.append(value)
                        elif key == 'date_from':
                            conditions.append("timestamp >= %s")
                            params.append(value)
                        elif key == 'date_to':
                            conditions.append("timestamp <= %s")
                            params.append(value)
                    
                    if conditions:
                        query += " WHERE " + " AND ".join(conditions)
                
                query += " ORDER BY timestamp DESC LIMIT %s OFFSET %s"
                params.extend([limit, offset])
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                cursor.close()
                
                return results
                
        except Error as e:
            logger.error(f"Error retrieving conversations: {e}")
            return []
    
    def get_conversation_stats(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get conversation statistics.
        
        Args:
            filters: Dictionary of filter criteria
            
        Returns:
            Dictionary containing statistics
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                
                # Base query for counting
                base_query = "SELECT COUNT(*) as total_conversations FROM conversations"
                params = []
                
                # Apply filters if provided
                if filters:
                    conditions = []
                    for key, value in filters.items():
                        if key in ['ip_address', 'product', 'session_id']:
                            conditions.append(f"{key} = %s")
                            params.append(value)
                        elif key == 'date_from':
                            conditions.append("timestamp >= %s")
                            params.append(value)
                        elif key == 'date_to':
                            conditions.append("timestamp <= %s")
                            params.append(value)
                    
                    if conditions:
                        base_query += " WHERE " + " AND ".join(conditions)
                
                # Get total count
                cursor.execute(base_query, params)
                total_result = cursor.fetchone()
                total_conversations = total_result['total_conversations']
                
                # Get additional statistics
                stats_query = """
                SELECT 
                    AVG(response_time_ms) as avg_response_time,
                    MAX(response_time_ms) as max_response_time,
                    MIN(response_time_ms) as min_response_time,
                    AVG(confidence_score) as avg_confidence_score,
                    COUNT(DISTINCT ip_address) as unique_users,
                    COUNT(DISTINCT product) as unique_products,
                    COUNT(DISTINCT session_id) as unique_sessions
                FROM conversations
                """
                
                if filters and conditions:
                    stats_query += " WHERE " + " AND ".join(conditions)
                
                cursor.execute(stats_query, params)
                stats_result = cursor.fetchone()
                cursor.close()
                
                return {
                    'total_conversations': total_conversations,
                    'avg_response_time_ms': float(stats_result['avg_response_time']) if stats_result['avg_response_time'] else 0,
                    'max_response_time_ms': stats_result['max_response_time'] or 0,
                    'min_response_time_ms': stats_result['min_response_time'] or 0,
                    'avg_confidence_score': float(stats_result['avg_confidence_score']) if stats_result['avg_confidence_score'] else 0,
                    'unique_users': stats_result['unique_users'] or 0,
                    'unique_products': stats_result['unique_products'] or 0,
                    'unique_sessions': stats_result['unique_sessions'] or 0
                }
                
        except Error as e:
            logger.error(f"Error getting conversation stats: {e}")
            return {}


# Global database manager instance
db_manager = DatabaseManager()
