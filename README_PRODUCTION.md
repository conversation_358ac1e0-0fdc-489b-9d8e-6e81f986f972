# Chat-Jina Production Deployment Guide v2.0

## 📋 Panoramica

Questa è la build di produzione ottimizzata per il sistema Chat-Jina v2.0, un chatbot intelligente per prodotti che utilizza AI generativa e embeddings per fornire risposte contestuali basate su documenti PDF.

## 🚀 Avvio Rapido

### 1. Prerequisiti

- **Python 3.8+** installato sul sistema
- **MySQL Server** (opzionale, per il logging delle conversazioni)
- **Connessione Internet** (per le API Jina e Gemini)

### 2. Configurazione

1. **Copia il template delle variabili d'ambiente:**
   ```bash
   cp .env.template .env
   ```

2. **Configura le API keys nel file `.env`:**
   ```bash
   nano .env
   ```
   
   Compila almeno questi campi obbligatori:
   - `JINA_API_KEY`: La tua API key di Jina
   - `GEMINI_API_KEY`: La tua API key di Google Gemini

### 3. <PERSON><PERSON>vio dell'Applicazione

**Metodo 1 - Script automatico (Linux/macOS):**
```bash
./start.sh
```

**Metodo 2 - Avvio manuale:**
```bash
pip3 install -r requirements.txt
python3 start_production.py
```

L'applicazione sarà disponibile su: `http://localhost:5001`

## 🔧 Configurazione Avanzata

### Variabili d'Ambiente

| Variabile | Descrizione | Default | Obbligatoria |
|-----------|-------------|---------|--------------|
| `JINA_API_KEY` | API key per Jina embeddings | - | ✅ |
| `GEMINI_API_KEY` | API key per Google Gemini | - | ✅ |
| `VERBOSITY_LEVEL` | Livello di verbosità (1-5) | 2 | ❌ |
| `HOST` | Indirizzo IP del server | 0.0.0.0 | ❌ |
| `PORT` | Porta del server | 5001 | ❌ |
| `PERFORMANCE_PROFILE` | Profilo performance (fast/balanced/quality) | balanced | ❌ |

### Database MySQL (Opzionale)

Se vuoi abilitare il logging delle conversazioni:

1. **Crea il database:**
   ```sql
   mysql -u root -p < database_setup.sql
   ```

2. **Configura le credenziali nel file `.env`:**
   ```
   DB_HOST=localhost
   DB_USER=your_username
   DB_PASSWORD=your_password
   DB_NAME=chat_jina_prod
   ```

## 📁 Struttura dei File

```
dist/
├── app.py                    # Applicazione Flask principale
├── pdf_chatbot_prodotti.py   # Logica del chatbot
├── performance_config.py     # Configurazioni performance
├── start_production.py       # Script di avvio produzione v2.0
├── start.sh                  # Script di avvio automatico
├── .env.template             # Template variabili d'ambiente
├── requirements.txt          # Dipendenze Python ottimizzate
├── database_setup.sql        # Setup database MySQL
├── pdf_metadata.json         # Metadati documenti PDF
├── schedule_config.json      # Configurazione scheduling
├── api/                      # API endpoints
├── database/                 # Gestione database
├── guardrails_py/            # Sistema di sicurezza
├── logging_service/          # Sistema di logging
├── middleware/               # Middleware Flask
├── scripts/                  # Script di utilità
├── static/                   # File statici (CSS, JS)
├── templates/                # Template HTML
├── pdf/                      # Documenti PDF
├── logs/                     # Directory per i log
└── chromadb_data/            # Directory per ChromaDB
```

## 🔒 Sicurezza

### Guardrails Integrati

Il sistema include guardrails automatici per:
- **Rilevamento contenuti inappropriati**
- **Filtraggio linguaggio offensivo**
- **Validazione input utente**
- **Protezione da injection attacks**

### Best Practices

1. **Non esporre mai le API keys** nei log o nel codice
2. **Usa HTTPS** in produzione
3. **Configura un firewall** appropriato
4. **Monitora i log** regolarmente
5. **Aggiorna le dipendenze** periodicamente

## 📊 Monitoring e Performance

### Profili di Performance

- **`fast`**: Massima velocità, qualità ridotta
- **`balanced`**: Bilanciamento velocità/qualità (default)
- **`quality`**: Massima qualità, velocità ridotta

### Log Files

- `logs/app_production.log`: Log principale dell'applicazione
- `logs/conversation.log`: Log delle conversazioni (se database abilitato)

## 🐛 Troubleshooting

### Problemi Comuni

1. **"API key mancante"**
   - Verifica che `.env` sia configurato correttamente
   - Controlla che le API keys siano valide

2. **"Errore connessione database"**
   - Il database MySQL è opzionale
   - Verifica le credenziali in `.env`

3. **"Porta già in uso"**
   - Cambia la porta nel file `.env`: `PORT=5002`

4. **"Documenti PDF non trovati"**
   - Verifica che la cartella `pdf/` contenga i documenti
   - Controlla i permessi di lettura

### Log di Debug

Per abilitare log più dettagliati:
```bash
export VERBOSITY_LEVEL=4
python3 start_production.py
```

## 🔄 Aggiornamenti

Per aggiornare l'applicazione:

1. **Backup dei dati:**
   ```bash
   cp -r chromadb_data chromadb_data.backup
   cp .env .env.backup
   ```

2. **Sostituisci i file** con la nuova versione

3. **Aggiorna le dipendenze:**
   ```bash
   pip3 install -r requirements.txt --upgrade
   ```

## 📞 Supporto

Per problemi o domande:
- Controlla i log in `logs/app_production.log`
- Verifica la configurazione in `.env`
- Consulta la documentazione delle API utilizzate

---

**Versione Build:** Production v2.0  
**Data Build:** $(date)  
**Ottimizzato per:** Ambiente di produzione
