# Template per le variabili d'ambiente di produzione
# Copia questo file in .env e compila con i tuoi valori

# === API KEYS (OBBLIGATORIE) ===
JINA_API_KEY=your_jina_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# === CONFIGURAZIONI APPLICAZIONE ===
VERBOSITY_LEVEL=2
FLASK_ENV=production
FLASK_DEBUG=False

# === CONFIGURAZIONI SERVER ===
HOST=0.0.0.0
PORT=5001

# === CONFIGURAZIONI DATABASE (OPZIONALI) ===
DB_HOST=localhost
DB_PORT=3306
DB_NAME=chat_jina_prod
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# === CONFIGURAZIONI LOGGING ===
LOG_LEVEL=INFO
LOG_FILE=logs/app_production.log

# === CONFIGURAZIONI PERFORMANCE ===
PERFORMANCE_PROFILE=balanced
CACHE_ENABLED=True
GUARDRAILS_ENABLED=True

# === CONFIGURAZIONI SICUREZZA ===
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# === CONFIGURAZIONI CHROMADB ===
CHROMADB_PATH=./chromadb_data
CHROMADB_COLLECTION_NAME=product_documents
