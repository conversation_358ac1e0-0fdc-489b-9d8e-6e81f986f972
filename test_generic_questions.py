#!/usr/bin/env python3
"""
Script di test per verificare la gestione delle domande generiche
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Aggiungi il percorso del progetto
sys.path.append(str(Path(__file__).parent))

# Carica le variabili d'ambiente
load_dotenv()

from pdf_chatbot_prodotti import ProductChatbot
import logging

# Configura logging per il test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_generic_questions():
    """Testa la gestione delle domande generiche vs specifiche"""
    
    # Verifica le API keys
    jina_api_key = os.getenv("JINA_API_KEY")
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    if not jina_api_key or not gemini_api_key:
        logger.error("❌ API keys non configurate. Controlla il file .env")
        return False
    
    logger.info("🚀 Inizializzazione chatbot per test...")
    
    try:
        # Inizializza il chatbot
        chatbot = ProductChatbot(
            jina_api_key=jina_api_key,
            gemini_api_key=gemini_api_key,
            verbosity_level=3,
            force_reprocess=False
        )
        
        # Prepara i documenti per SYM
        product_code = "SYM"
        documents_ready = chatbot.prepare_product_documents(product_code)
        
        if not documents_ready:
            logger.error(f"❌ Documenti non pronti per {product_code}")
            return False
        
        logger.info(f"✅ Documenti pronti per {product_code}")
        
        # Test con domande di diversi tipi
        test_questions = [
            {
                "question": "Ciao come stai?",
                "type": "generica",
                "expected_fallback": True
            },
            {
                "question": "Qual è il significato della vita?",
                "type": "generica",
                "expected_fallback": True
            },
            {
                "question": "Qual è la coppia massima del motore?",
                "type": "specifica",
                "expected_fallback": False
            },
            {
                "question": "Qual è la cilindrata?",
                "type": "specifica", 
                "expected_fallback": False
            },
            {
                "question": "Come si fa la manutenzione?",
                "type": "semi-specifica",
                "expected_fallback": False  # Dovrebbe trovare info sulla manutenzione
            }
        ]
        
        logger.info(f"\n{'='*60}")
        logger.info("🧪 INIZIO TEST DOMANDE")
        logger.info(f"{'='*60}")
        
        results = []
        
        for i, test_case in enumerate(test_questions, 1):
            question = test_case["question"]
            question_type = test_case["type"]
            expected_fallback = test_case["expected_fallback"]
            
            logger.info(f"\n--- TEST {i}/5 ---")
            logger.info(f"❓ Domanda ({question_type}): {question}")
            
            try:
                # Esegui la query
                response, _, guardrail_data = chatbot.search_and_answer(
                    query=question,
                    product_code=product_code,
                    chat_history=[]
                )
                
                # Verifica se è stata attivata la risposta di fallback
                is_fallback = "Non ho trovato informazioni pertinenti nei documenti" in response
                
                logger.info(f"🤖 Risposta: {response[:100]}...")
                logger.info(f"🎯 Fallback attivato: {'SÌ' if is_fallback else 'NO'}")
                logger.info(f"✅ Comportamento atteso: {'SÌ' if expected_fallback else 'NO'}")
                
                # Verifica se il comportamento è quello atteso
                test_passed = (is_fallback == expected_fallback)
                logger.info(f"📊 Test: {'✅ PASSATO' if test_passed else '❌ FALLITO'}")
                
                results.append({
                    "question": question,
                    "type": question_type,
                    "expected_fallback": expected_fallback,
                    "actual_fallback": is_fallback,
                    "passed": test_passed,
                    "response": response
                })
                
            except Exception as e:
                logger.error(f"❌ Errore durante il test: {e}")
                results.append({
                    "question": question,
                    "type": question_type,
                    "expected_fallback": expected_fallback,
                    "actual_fallback": False,
                    "passed": False,
                    "response": f"ERRORE: {e}"
                })
        
        # Riepilogo risultati
        logger.info(f"\n{'='*60}")
        logger.info("📊 RIEPILOGO RISULTATI TEST")
        logger.info(f"{'='*60}")
        
        passed_tests = sum(1 for r in results if r["passed"])
        total_tests = len(results)
        
        logger.info(f"✅ Test passati: {passed_tests}/{total_tests}")
        
        for i, result in enumerate(results, 1):
            status = "✅" if result["passed"] else "❌"
            logger.info(f"{status} Test {i}: {result['type']} - {result['question'][:30]}...")
        
        if passed_tests == total_tests:
            logger.info("🎉 TUTTI I TEST SONO PASSATI!")
            return True
        else:
            logger.warning(f"⚠️ {total_tests - passed_tests} test falliti")
            return False
            
    except Exception as e:
        logger.error(f"❌ Errore durante l'inizializzazione: {e}")
        return False

if __name__ == "__main__":
    success = test_generic_questions()
    sys.exit(0 if success else 1)
